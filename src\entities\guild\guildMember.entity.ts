import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, Index } from 'typeorm';
import { GuildEntity } from './guild.entity';
import { GuildHallEntity } from './guildHall.entity';

// 帮派成员表
@Entity()
@Index(['guild', 'userId'], { unique: true }) // 联合唯一索引，确保用户在同一帮派中只有一条记录
export class GuildMemberEntity {
    @PrimaryGeneratedColumn()
    id: number;

    // 关联帮派
    @ManyToOne(() => GuildEntity, guild => guild.id)
    guild: GuildEntity;

    // 用户ID
    @Column({ type: 'int' })
    @Index()
    userId: number;

    // 用户名称
    @Column({ type: 'varchar', length: 20 })
    userName: string;

    // 职位 (1:帮主 2:副帮主 3:堂主  4:普通成员)
    @Column({ type: 'int', default: 4 })
    @Index()
    position: number;

    // 关联分堂 (可选，普通成员可以属于某个分堂)
    @ManyToOne(() => GuildHallEntity, hall => hall.id, { nullable: true })
    hall: GuildHallEntity;

    // 贡献度
    @Column({ type: 'bigint', default: 0, unsigned: true })
    contribution: number;

    // 加入时间
    @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    joinDate: Date;

    // 最后活跃时间
    @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
    lastActiveDate: Date;

    // 状态 (1:正常 2:已退出)
    @Column({ type: 'int', default: 1 })
    @Index()
    status: number;
}
