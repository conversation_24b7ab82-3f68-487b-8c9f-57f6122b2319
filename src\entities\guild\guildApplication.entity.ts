import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, Index } from 'typeorm';
import { GuildEntity } from './guild.entity';

// 帮派申请表
@Entity()
@Index(['guild', 'userId'], { unique: true }) // 联合唯一索引，确保用户对同一帮派只能有一个待处理申请
export class GuildApplicationEntity {
    @PrimaryGeneratedColumn()
    id: number;

    // 关联帮派
    @ManyToOne(() => GuildEntity, guild => guild.id)
    guild: GuildEntity;

    // 申请人ID
    @Column({ type: 'int' })
    @Index()
    userId: number;

    // 申请人名称
    @Column({ type: 'varchar', length: 20 })
    userName: string;

    // 申请留言
    @Column({ type: 'varchar', length: 200, default: '' })
    message: string;

    // 状态 (1:待审核 2:已同意 3:已拒绝)
    @Column({ type: 'int', default: 1 })
    @Index()
    status: number;

    // 审核人ID
    @Column({ type: 'int', nullable: true })
    reviewerId: number;

    // 审核人名称
    @Column({ type: 'varchar', length: 20, nullable: true })
    reviewerName: string;

    // 审核时间
    @Column({ type: 'timestamp', nullable: true })
    reviewDate: Date;

    // 申请时间
    @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;
}
